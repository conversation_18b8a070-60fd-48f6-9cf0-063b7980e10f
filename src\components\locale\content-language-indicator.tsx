/**
 * ContentLanguageIndicator Component
 *
 * =============================================================================
 * COMPONENT PURPOSE & USAGE SCENARIOS
 * =============================================================================
 *
 * This component is specifically designed for CONTENT DETAIL PAGE HEADERS.
 * It provides a compact language switching interface with clear availability
 * indicators, perfect for placement in page headers alongside metadata.
 *
 * CURRENT USAGE IN CODEBASE:
 * ✅ /blogs/[slug]/page.tsx - Blog detail page header (line 79)
 * ✅ /products/[slug]/page.tsx - Product detail page header (line 79)
 * ✅ /case-studies/[slug]/page.tsx - Case study detail page header (line 79)
 *
 * VISUAL CHARACTERISTICS:
 * - Uses Material Design icons (MdLanguage, MdCheck, MdClose)
 * - Shows availability status with ✓ (green) / ✗ (red) icons
 * - Compact design suitable for headers
 * - Language buttons with clear visual feedback
 *
 * WHEN TO USE THIS COMPONENT:
 * ✅ Content detail page headers
 * ✅ Compact spaces requiring language switching
 * ✅ When you need clear availability indicators
 *
 * WHEN NOT TO USE (use LanguageVersions instead):
 * ❌ Sidebars or content areas with more space
 * ❌ When you need to show content titles
 * ❌ Admin interfaces requiring detailed language info
 *
 * =============================================================================
 */

"use client";

// Next.js Navigation Hooks - For routing and URL management
import { useParams, usePathname, useRouter } from "next/navigation";

// Toast Notifications - For user feedback during language switching
import { toast } from "sonner";

// UI Components - Shadcn/ui components for consistent design
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

// Icons - Material Design icons for visual elements
import { MdLanguage, MdCheck, MdClose } from "react-icons/md";

// Internationalization - Locale configuration and names
import { localeNames, locales } from "@/i18n/locale";

// Content Management Services - Core functions for content language handling
import {
  detectContentPage,           // Detects content type and slug from URL
  getAvailableLanguageVersions, // Gets all language versions for content
  handleContentLanguageSwitch, // Handles intelligent language switching
  getContentTitle             // Retrieves content title for display
} from "@/services/content";

/**
 * Props interface for the ContentLanguageIndicator component
 */
interface ContentLanguageIndicatorProps {
  /**
   * Custom CSS class name for additional styling
   */
  className?: string;
  /**
   * Display variant for the component
   *
   * USAGE EXAMPLES:
   *
   * 'compact' - Used in content page headers (MOST COMMON):
   * ```tsx
   * // In blog/product/case-study detail pages
   * <div className="flex items-center justify-between">
   *   <div>Tags and metadata</div>
   *   <ContentLanguageIndicator variant="compact" />
   * </div>
   * ```
   *
   * 'full' - Used in dedicated language selection areas:
   * ```tsx
   * // In content management or detailed language info sections
   * <ContentLanguageIndicator variant="full" className="mb-4" />
   * ```
   */
  variant?: 'compact' | 'full';
}

/**
 * Content Language Indicator Component
 *
 * PURPOSE:
 * This component is specifically designed for CONTENT DETAIL PAGES (individual blog posts,
 * product pages, case studies). It shows language availability with clear visual indicators
 * (✓ for available, ✗ for unavailable) and provides compact language switching.
 *
 * USAGE LOCATIONS:
 * - Blog detail pages: /blogs/[slug] (top-right corner)
 * - Product detail pages: /products/[slug] (top-right corner)
 * - Case study detail pages: /case-studies/[slug] (top-right corner)
 *
 * KEY DIFFERENCES FROM LanguageVersions:
 * - Shows availability status with icons (✓/✗)
 * - More compact design for header placement
 * - Focuses on quick language switching
 * - Uses Material Design icons (MdLanguage, MdCheck, MdClose)
 *
 * VISUAL DESIGN:
 * - Compact: Horizontal buttons with language names + status icons
 * - Full: Card layout with detailed availability information
 *
 * BEHAVIOR:
 * - Only appears on content detail pages (not list pages)
 * - Shows all supported languages with availability status
 * - Handles intelligent switching with fallback to list pages
 * - Provides toast notifications for unavailable content
 *
 * @param className - Additional CSS classes for styling
 * @param variant - Display mode ('compact' for headers, 'full' for detailed view)
 */
export default function ContentLanguageIndicator({
  className = "",
  variant = 'compact'
}: ContentLanguageIndicatorProps) {
  // Next.js hooks for navigation and URL management
  const params = useParams();                    // Get URL parameters (including locale)
  const locale = params.locale as string;        // Extract current locale from URL
  const router = useRouter();                    // Router for programmatic navigation
  const pathname = usePathname();                // Current pathname for content detection

  // Detect current content page information from URL
  // This analyzes the pathname to determine content type and slug
  const contentInfo = detectContentPage(pathname, locale);

  // Early return: Only show component for actual content pages with slugs
  // Prevents showing on list pages or non-content pages
  if (contentInfo.type === 'other' || !contentInfo.slug) {
    return null;
  }

  // Get all available language versions for this specific content
  // This checks which languages have translations of the current content
  const languageVersions = getAvailableLanguageVersions(
    contentInfo.type,    // Content type (blog, product, case-study)
    contentInfo.slug,    // Content identifier (slug)
    locales             // All supported locales from i18n config
  );

  // Get the title of the current content for display purposes
  const currentTitle = getContentTitle(contentInfo.type, contentInfo.slug, locale);

  /**
   * Handle language switching with intelligent fallback
   *
   * This function attempts to switch to the target language version of the current content.
   * If the content doesn't exist in the target language, it falls back to the content list
   * page and shows a user-friendly notification.
   *
   * @param targetLocale - The locale to switch to (e.g., 'en', 'zh')
   */
  const handleLanguageSwitch = (targetLocale: string) => {
    // Only proceed if switching to a different language
    if (targetLocale !== locale) {
      // Use the intelligent language switching utility
      // This handles both direct switching and fallback scenarios
      const switchResult = handleContentLanguageSwitch(
        pathname,      // Current page path
        locale,        // Current language
        targetLocale   // Target language
      );

      // Navigate to the determined URL (either direct content or fallback list)
      router.push(switchResult.url);

      // Provide user feedback for fallback scenarios
      // When content doesn't exist in target language, user is redirected to list page
      if (switchResult.strategy === 'fallback-list') {
        toast.info(
          `This content is not available in ${localeNames[targetLocale]}. Redirected to the content list.`,
          {
            duration: 4000,
          }
        );
      }
    }
  };

  // Render compact variant - horizontal button layout
  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {/* Language icon indicator */}
        <MdLanguage className="text-muted-foreground" size={16} />

        {/* Language buttons container */}
        <div className="flex items-center gap-1">
          {languageVersions.map((version) => (
            <Button
              key={version.locale}
              variant={version.locale === locale ? "default" : "outline"}
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => handleLanguageSwitch(version.locale)}
              disabled={!version.exists && version.locale !== locale}
            >
              <span className="flex items-center gap-1">
                {/* Language name */}
                {localeNames[version.locale]}
                {/* Availability indicator icon */}
                {version.exists ? (
                  <MdCheck size={12} className="text-green-500" />
                ) : (
                  <MdClose size={12} className="text-red-500" />
                )}
              </span>
            </Button>
          ))}
        </div>
      </div>
    );
  }

  // Render full variant - detailed card layout
  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Language icon */}
          <MdLanguage className="text-muted-foreground mt-1" size={20} />

          <div className="flex-1">
            {/* Card title */}
            <h4 className="font-medium text-sm mb-2">Language Versions</h4>

            {/* Current content title display */}
            {currentTitle && (
              <p className="text-xs text-muted-foreground mb-3">
                Current: {currentTitle}
              </p>
            )}

            {/* Language versions list */}
            <div className="space-y-2">
              {languageVersions.map((version) => (
                <div key={version.locale} className="flex items-center justify-between">
                  {/* Left side: Language info and availability status */}
                  <div className="flex items-center gap-2">
                    {/* Language badge */}
                    <Badge
                      variant={version.locale === locale ? "default" : "outline"}
                      className="text-xs"
                    >
                      {localeNames[version.locale]}
                    </Badge>

                    {/* Availability status with icon */}
                    {version.exists ? (
                      <div className="flex items-center gap-1 text-xs text-green-600">
                        <MdCheck size={12} />
                        <span>Available</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-xs text-red-600">
                        <MdClose size={12} />
                        <span>Not available</span>
                      </div>
                    )}
                  </div>

                  {/* Right side: Action button (only for non-current languages) */}
                  {version.locale !== locale && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => handleLanguageSwitch(version.locale)}
                      disabled={!version.exists}
                    >
                      {/* Button text changes based on availability */}
                      {version.exists ? 'Switch' : 'Go to list'}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
