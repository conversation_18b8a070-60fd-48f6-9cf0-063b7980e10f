/**
 * Test script for language switching functionality
 * 
 * This script tests the content language switching utilities to ensure
 * they work correctly in various scenarios.
 */

import {
  detectContentPage,
  contentExistsInLocale,
  getAvailableLanguageVersions,
  handleContentLanguageSwitch,
  generateContentUrl
} from '../src/services/content/index.js'

// Test data
const testCases = [
  {
    name: 'Blog page detection',
    pathname: '/blogs/getting-started-with-shipany',
    locale: 'en',
    expected: {
      type: 'blog',
      slug: 'getting-started-with-shipany'
    }
  },
  {
    name: 'Chinese blog page detection',
    pathname: '/zh/blogs/getting-started-with-shipany',
    locale: 'zh',
    expected: {
      type: 'blog',
      slug: 'getting-started-with-shipany'
    }
  },
  {
    name: 'Product page detection',
    pathname: '/products/ai-content-generator',
    locale: 'en',
    expected: {
      type: 'product',
      slug: 'ai-content-generator'
    }
  },
  {
    name: 'Case study page detection',
    pathname: '/case-studies/techcorp-ai-transformation',
    locale: 'en',
    expected: {
      type: 'case-study',
      slug: 'techcorp-ai-transformation'
    }
  },
  {
    name: 'Non-content page detection',
    pathname: '/pricing',
    locale: 'en',
    expected: {
      type: 'other',
      slug: null
    }
  }
]

const languageSwitchTests = [
  {
    name: 'Switch from English blog to Chinese (exists)',
    pathname: '/blogs/getting-started-with-shipany',
    currentLocale: 'en',
    targetLocale: 'zh',
    expectedStrategy: 'direct'
  },
  {
    name: 'Switch from English blog to Chinese (not exists)',
    pathname: '/blogs/english-only-test',
    currentLocale: 'en',
    targetLocale: 'zh',
    expectedStrategy: 'fallback-list'
  },
  {
    name: 'Switch from non-content page',
    pathname: '/pricing',
    currentLocale: 'en',
    targetLocale: 'zh',
    expectedStrategy: 'direct'
  }
]

console.log('🧪 Testing Content Language Switching Utilities\n')

// Test content page detection
console.log('📍 Testing Content Page Detection:')
testCases.forEach(test => {
  const result = detectContentPage(test.pathname, test.locale)
  const passed = result.type === test.expected.type && result.slug === test.expected.slug
  
  console.log(`  ${passed ? '✅' : '❌'} ${test.name}`)
  if (!passed) {
    console.log(`    Expected: ${JSON.stringify(test.expected)}`)
    console.log(`    Got: ${JSON.stringify({ type: result.type, slug: result.slug })}`)
  }
})

console.log('\n🔄 Testing Language Switching Logic:')
languageSwitchTests.forEach(test => {
  const result = handleContentLanguageSwitch(
    test.pathname,
    test.currentLocale,
    test.targetLocale,
    ['en', 'zh']
  )
  
  const passed = result.strategy === test.expectedStrategy
  
  console.log(`  ${passed ? '✅' : '❌'} ${test.name}`)
  console.log(`    Strategy: ${result.strategy}`)
  console.log(`    URL: ${result.url}`)
  if (result.reason) {
    console.log(`    Reason: ${result.reason}`)
  }
  if (!passed) {
    console.log(`    Expected strategy: ${test.expectedStrategy}`)
  }
  console.log()
})

// Test URL generation
console.log('🔗 Testing URL Generation:')
const urlTests = [
  { type: 'blog', slug: 'test-post', locale: 'en', expected: '/blogs/test-post' },
  { type: 'blog', slug: 'test-post', locale: 'zh', expected: '/zh/blogs/test-post' },
  { type: 'product', slug: 'test-product', locale: 'en', expected: '/products/test-product' },
  { type: 'case-study', slug: 'test-case', locale: 'zh', expected: '/zh/case-studies/test-case' }
]

urlTests.forEach(test => {
  const result = generateContentUrl(test.type, test.slug, test.locale)
  const passed = result === test.expected
  
  console.log(`  ${passed ? '✅' : '❌'} ${test.type} (${test.locale}): ${result}`)
  if (!passed) {
    console.log(`    Expected: ${test.expected}`)
  }
})

console.log('\n🎉 Testing completed!')
