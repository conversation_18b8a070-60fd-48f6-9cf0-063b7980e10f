/**
 * Language Switching Service
 * 
 * This service handles intelligent language switching logic for content pages.
 * It provides smart navigation between different language versions of content
 * with fallback strategies when content doesn't exist in the target language.
 * 
 * Key Features:
 * - Intelligent language switching with fallback strategies
 * - Content availability checking before navigation
 * - User-friendly fallback to list pages when content is unavailable
 * - Comprehensive language version information for UI components
 * 
 * Switching Strategies:
 * 1. 'direct': Content exists in target language - navigate directly to it
 * 2. 'fallback-list': Content doesn't exist - navigate to the content list page
 * 
 * This approach ensures users never encounter 404 errors when switching languages,
 * instead providing them with relevant content in their preferred language.
 */

import type { ContentType, LanguageVersion, LanguageSwitchResult } from './types'
import { detectContentPage } from './content-detection'
import { contentExistsInLocale } from './content-queries'
import { generateContentUrl } from './url-generation'

/**
 * Get all available language versions for a piece of content
 * 
 * This function creates a comprehensive list of language versions for a specific
 * content item, including availability status and URLs for each supported locale.
 * It's used by UI components to display language switching options.
 * 
 * The function checks each supported locale to see if the content exists in that
 * language and generates the appropriate URL for accessing it.
 * 
 * @param contentType - Type of content ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param supportedLocales - Array of all supported locale codes
 * @returns Array of LanguageVersion objects with availability and URL info
 */
export function getAvailableLanguageVersions(
  contentType: ContentType,
  slug: string,
  supportedLocales: string[]
): LanguageVersion[] {
  // For non-content pages or missing slugs, mark all languages as unavailable
  if (contentType === 'other' || !slug) {
    return supportedLocales.map(locale => ({
      locale,
      exists: false,
      url: generateContentUrl(contentType, slug, locale)
    }))
  }

  // For each supported locale, check if content exists and generate URL
  return supportedLocales.map(locale => {
    // Check if this specific content exists in this language
    const exists = contentExistsInLocale(contentType, slug, locale)
    // Generate the URL for this language version
    const url = generateContentUrl(contentType, slug, locale)
    
    return {
      locale,
      exists,
      url
    }
  })
}

/**
 * Handle intelligent language switching for content pages
 *
 * This is the core function for smart language switching. It attempts to navigate
 * users to the same content in a different language, but provides intelligent
 * fallback behavior when the content doesn't exist in the target language.
 * 
 * Switching Strategies:
 * 1. 'direct': Content exists in target language - navigate directly to it
 * 2. 'fallback-list': Content doesn't exist - navigate to the content list page
 * 
 * This approach ensures users never encounter 404 errors when switching languages,
 * instead providing them with relevant content in their preferred language.
 *
 * @param pathname - Current page pathname from Next.js router
 * @param currentLocale - Current language locale
 * @param targetLocale - Desired target language locale
 * @returns Object containing target URL, strategy used, and optional reason
 */
export function handleContentLanguageSwitch(
  pathname: string,
  currentLocale: string,
  targetLocale: string
): LanguageSwitchResult {
  // First, analyze the current page to understand what content we're viewing
  const contentInfo = detectContentPage(pathname, currentLocale)

  // Check if the same content exists in the target language
  const contentExists = contentInfo.slug && contentExistsInLocale(contentInfo.type, contentInfo.slug, targetLocale)

  // Strategy 1: Direct navigation - content exists in target language
  if (contentExists && contentInfo.slug) {
    return {
      url: generateContentUrl(contentInfo.type, contentInfo.slug, targetLocale),
      strategy: 'direct'
    }
  }

  // Strategy 2: Fallback to list page - content doesn't exist in target language
  // This ensures users still get relevant content in their preferred language
  const listUrl = generateContentUrl(contentInfo.type, '', targetLocale)

  return {
    url: listUrl,
    strategy: 'fallback-list',
    reason: contentInfo.slug
      ? `Content "${contentInfo.slug}" not available in target language`
      : `Switched to ${targetLocale} content list`
  }
}

/**
 * Check if language switching should be available for the current page
 * 
 * This function determines whether language switching UI should be shown
 * based on the current page type and available content.
 * 
 * @param pathname - Current page pathname
 * @param currentLocale - Current language locale
 * @param supportedLocales - Array of all supported locale codes
 * @returns true if language switching should be available, false otherwise
 */
export function shouldShowLanguageSwitching(
  pathname: string,
  currentLocale: string,
  supportedLocales: string[]
): boolean {
  const contentInfo = detectContentPage(pathname, currentLocale)
  
  // Don't show on non-content pages
  if (contentInfo.type === 'other' || !contentInfo.slug) {
    return false
  }

  // Check if there are multiple language versions available
  const languageVersions = getAvailableLanguageVersions(
    contentInfo.type,
    contentInfo.slug,
    supportedLocales
  )
  
  const availableVersions = languageVersions.filter(version => version.exists)
  
  // Only show if there are multiple language versions
  return availableVersions.length > 1
}
