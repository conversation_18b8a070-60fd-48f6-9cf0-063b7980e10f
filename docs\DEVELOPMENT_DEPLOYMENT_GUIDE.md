# 开发、测试和部署指南

本文档详细说明了 ShipAny 项目中各个构建命令的作用、使用时机以及完整的开发部署工作流程。

## 核心命令详解

### 1. `contentlayer build`

**作用**：

- 处理 `content/` 目录下的所有 MDX 文件
- 将 MDX 内容转换为 TypeScript 类型安全的数据结构
- 生成 `.contentlayer/generated/` 目录，包含所有内容的索引和类型定义
- 支持多语言内容（en/zh）和多种内容类型（blogs、products、case-studies）

**何时运行**：

- 内容文件发生变化时
- 构建前必须运行
- 开发环境下自动监听文件变化

**运行方式**：

```bash
# 使用 pnpm 运行（推荐）
pnpm contentlayer build

# 或者使用 npx
npx contentlayer build
```

**注意**：`contentlayer build` 无法直接运行，必须通过包管理器执行。

**输出文件**：

- `.contentlayer/generated/index.mjs` - 内容索引
- `.contentlayer/generated/types.d.ts` - TypeScript 类型定义

### 2. `generate:sitemap`

**作用**：

- 生成 `public/sitemap.xml` 文件
- 包含所有静态页面和动态内容页面的 URL
- 支持多语言 SEO（hreflang 属性）
- 为搜索引擎提供网站结构信息

**依赖**：需要先运行 `contentlayer build` 来获取内容数据

**输出文件**：

- `public/sitemap.xml` - 网站地图

### 3. `generate:rss`

**作用**：

- 为每种语言生成 RSS 订阅源
- 英文：`public/rss.xml`
- 中文：`public/rss-zh.xml`
- 包含最新 20 篇博客文章的摘要信息

**依赖**：需要先运行 `contentlayer build` 来获取博客数据

**输出文件**：

- `public/rss.xml` - 英文 RSS 订阅
- `public/rss-zh.xml` - 中文 RSS 订阅

### 4. `generate:content`

**作用**：

- 组合命令，等同于 `generate:sitemap && generate:rss`
- 一次性生成所有 SEO 相关的静态文件

**实际执行**：

```bash
pnpm generate:sitemap
pnpm generate:rss
```

### 5. `pnpm build`

**作用**：

- 完整的生产构建流程
- 执行顺序：`contentlayer build` → `generate:content` → `next build`
- 生成优化后的静态资源和服务器代码

**实际执行**：

```bash
contentlayer build
pnpm generate:content
next build
```

### 6. `pnpm dev`

**作用**：

- 启动开发服务器（使用 Turbopack 加速）
- 自动监听文件变化并热重载
- Contentlayer 在开发模式下自动处理内容变化

**特性**：

- 热重载支持
- 自动内容处理
- TypeScript 类型检查

## 工作流程图

### 开发阶段工作流

```mermaid
graph TD
    A[启动开发] --> B[pnpm dev]
    B --> C[Contentlayer 自动监听内容变化]
    C --> D[编辑 MDX 内容]
    D --> E[自动重新生成内容索引]
    E --> F[浏览器自动刷新]
    F --> D
```

### 生产部署工作流

```mermaid
graph TD
    A[准备部署] --> B[contentlayer build]
    B --> C[处理所有 MDX 内容]
    C --> D[generate:sitemap]
    D --> E[生成 sitemap.xml]
    E --> F[generate:rss]
    F --> G[生成 RSS 订阅源]
    G --> H[next build]
    H --> I[构建生产版本]
    I --> J[部署到服务器]
```

### 内容更新工作流

```mermaid
graph TD
    A[内容更新] --> B[修改/添加 MDX 文件]
    B --> C[contentlayer build]
    C --> D[generate:content]
    D --> E[pnpm build]
    E --> F[重新部署]
```

## 详细工作流程

### 开发环境启动

```bash
# 1. 启动开发服务器
pnpm dev

# 开发服务器会自动：
# - 启动 Next.js 开发服务器
# - 启动 Contentlayer 监听
# - 启用热重载
# - 监听文件变化
```

**流程说明**：

1. Next.js 启动开发服务器（端口 3000）
2. Contentlayer 自动处理现有 MDX 内容
3. 监听 `content/` 目录变化
4. 文件变化时自动重新处理内容
5. 浏览器自动刷新显示更新

### 内容开发流程

```bash
# 1. 创建新的 MDX 文件
# content/blogs/en/new-post.mdx
# content/blogs/zh/xin-wen-zhang.mdx

# 2. 编辑内容（开发服务器自动处理）
# - 添加 frontmatter
# - 编写 MDX 内容
# - 保存文件

# 3. 查看效果
# 访问 http://localhost:3000/blogs/new-post

# 4. 可选：手动更新 SEO 文件
pnpm generate:content
```

### 生产构建流程

```bash
# 方法一：一键构建（推荐）
pnpm build

# 方法二：分步构建
pnpm contentlayer build # 处理 MDX 内容
pnpm generate:sitemap   # 生成站点地图
pnpm generate:rss       # 生成 RSS 订阅
next build              # 构建 Next.js 应用

# 启动生产服务器
pnpm start
```

### CI/CD 部署流程

```yaml
# GitHub Actions 示例
name: Build and Deploy
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Build application
        run: pnpm build
        env:
          NEXT_PUBLIC_WEB_URL: ${{ secrets.WEB_URL }}
      
      - name: Deploy
        run: |
          # 部署到服务器的命令
          # 例如：rsync, docker, vercel 等
```

## 命令速查表

### 开发命令

```bash
# 启动开发环境
pnpm dev                    # 开发服务器 + 自动内容处理
pnpm dev --turbo           # 使用 Turbopack（默认已启用）

# 内容处理
contentlayer build         # 手动处理 MDX 内容
pnpm generate:content      # 生成 SEO 文件（sitemap + rss）
pnpm generate:sitemap      # 仅生成 sitemap.xml
pnpm generate:rss          # 仅生成 RSS 订阅源
```

### 构建命令

```bash
# 生产构建
pnpm build                 # 完整构建流程
pnpm start                 # 启动生产服务器
pnpm analyze               # 分析构建包大小

# 代码质量
pnpm lint                  # ESLint 检查
```

### 数据库命令

```bash
# 数据库操作
pnpm db:generate           # 生成数据库迁移文件
pnpm db:migrate            # 执行数据库迁移
pnpm db:studio             # 启动数据库管理界面
pnpm db:push               # 推送数据库架构变更
```

## 最佳实践

### 开发阶段

1. **使用开发服务器**：始终使用 `pnpm dev` 进行开发
2. **内容热重载**：依赖自动内容处理，无需手动运行构建命令
3. **类型检查**：利用 TypeScript 类型安全特性
4. **实时预览**：在浏览器中实时查看内容变化

### 内容管理

1. **统一命名**：文件名和 slug 保持一致
2. **多语言同步**：确保不同语言版本内容同步更新
3. **SEO 优化**：完善 frontmatter 元数据
4. **图片优化**：使用适当的图片格式和尺寸

### 部署准备

1. **完整构建**：使用 `pnpm build` 确保所有步骤执行
2. **环境变量**：设置正确的 `NEXT_PUBLIC_WEB_URL`
3. **依赖检查**：确保所有依赖正确安装
4. **构建验证**：本地测试生产构建结果

### 性能优化

1. **静态生成**：利用 Next.js 静态生成特性
2. **图片优化**：使用 Next.js Image 组件
3. **代码分割**：合理使用动态导入
4. **缓存策略**：配置适当的缓存头

## 故障排除

### 常见问题

1. **Contentlayer 构建失败**

   ```bash
   # 清理缓存重新构建
   rm -rf .contentlayer
   contentlayer build
   ```

2. **SEO 文件未更新**

   ```bash
   # 手动重新生成
   pnpm generate:content
   ```

3. **开发服务器问题**

   ```bash
   # 清理缓存重启
   rm -rf .next
   pnpm dev
   ```

4. **类型错误**

   ```bash
   # 重新生成类型定义
   contentlayer build
   ```

### 调试技巧

1. **检查生成内容**：查看 `.contentlayer/generated/` 目录
2. **验证 frontmatter**：确保 YAML 格式正确
3. **测试路由**：使用 `pnpm build --debug` 查看详细信息
4. **环境变量**：确认所有必需的环境变量已设置

## 总结

这套工作流程确保了：

- **开发效率**：自动化的内容处理和热重载
- **SEO 优化**：自动生成 sitemap 和 RSS 订阅
- **类型安全**：TypeScript 支持和类型检查
- **部署简化**：一键构建和部署流程
- **多语言支持**：完整的国际化解决方案

遵循这些工作流程可以确保项目的稳定性、可维护性和最佳性能。
